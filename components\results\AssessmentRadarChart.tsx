'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '../ui/chart';
import { Radar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, ResponsiveContainer } from 'recharts';
import { AssessmentScores } from '../../types/assessment-results';
import { BarChart3 } from 'lucide-react';

interface AssessmentRadarChartProps {
  scores: AssessmentScores;
}

export default function AssessmentRadarChart({ scores }: AssessmentRadarChartProps) {
  // Transform RIASEC scores to match the categories in your image
  // Mapping RIASEC categories to career-focused categories
  const radarData = [
    {
      category: 'Development',
      score: scores.riasec.investigative, // Investigative maps to Development (analytical, problem-solving)
      fullMark: 100,
    },
    {
      category: 'Design',
      score: scores.riasec.artistic, // Artistic maps to Design (creative, aesthetic)
      fullMark: 100,
    },
    {
      category: 'SMM',
      score: scores.riasec.social, // Social maps to SMM (social media, communication)
      fullMark: 100,
    },
    {
      category: 'Marketing',
      score: scores.riasec.enterprising, // Enterprising maps to Marketing (business, persuasion)
      fullMark: 100,
    },
    {
      category: 'Recruit',
      score: scores.riasec.conventional, // Conventional maps to Recruit (organized, systematic)
      fullMark: 100,
    },
  ];

  const chartConfig = {
    score: {
      label: "Score",
      color: "#6475e9",
    },
  };

  return (
    <Card className="bg-white border-[#eaecf0]">
      <CardHeader className="pb-4">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-[#e7eaff] rounded-lg">
            <BarChart3 className="w-5 h-5 text-[#6475e9]" />
          </div>
          <div>
            <CardTitle className="text-lg font-semibold text-[#1e1e1e]">
              Career Skills Assessment
            </CardTitle>
            <p className="text-xs text-[#64707d]">Analisis Kemampuan Berdasarkan RIASEC</p>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <ChartContainer config={chartConfig} className="mx-auto aspect-square max-h-[300px]">
          <RadarChart data={radarData}>
            <ChartTooltip 
              cursor={false} 
              content={<ChartTooltipContent hideLabel />} 
            />
            <PolarGrid 
              className="fill-[#f8fafc]" 
              stroke="#e2e8f0"
              strokeWidth={1}
            />
            <PolarAngleAxis 
              dataKey="category" 
              tick={{ 
                fill: '#64707d', 
                fontSize: 12,
                fontWeight: 500
              }}
              className="text-[#64707d]"
            />
            <PolarRadiusAxis 
              angle={90} 
              domain={[0, 100]} 
              tick={{ 
                fill: '#94a3b8', 
                fontSize: 10 
              }}
              tickCount={6}
              className="text-[#94a3b8]"
            />
            <Radar
              dataKey="score"
              stroke="#6475e9"
              fill="#6475e9"
              fillOpacity={0.2}
              strokeWidth={2}
              dot={{
                fill: '#6475e9',
                strokeWidth: 2,
                stroke: '#ffffff',
                r: 4,
              }}
            />
          </RadarChart>
        </ChartContainer>
        
        {/* Legend/Summary */}
        <div className="mt-4 space-y-3">
          <div className="grid grid-cols-2 gap-2 text-xs">
            {radarData.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-2 bg-[#f8fafc] rounded">
                <span className="font-medium text-[#1e1e1e]">{item.category}</span>
                <span className="text-[#6475e9] font-semibold">{item.score}</span>
              </div>
            ))}
          </div>

          {/* Overall Statistics */}
          <div className="flex items-center justify-between p-3 bg-gradient-to-r from-[#6475e9]/10 to-[#5a6bd8]/10 rounded-lg border border-[#6475e9]/20">
            <div className="text-center">
              <div className="text-lg font-bold text-[#6475e9]">
                {Math.round(radarData.reduce((sum, item) => sum + item.score, 0) / radarData.length)}
              </div>
              <div className="text-xs text-[#64707d]">Rata-rata</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-[#22c55e]">
                {Math.max(...radarData.map(item => item.score))}
              </div>
              <div className="text-xs text-[#64707d]">Tertinggi</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-[#f59e0b]">
                {radarData.find(item => item.score === Math.max(...radarData.map(i => i.score)))?.category}
              </div>
              <div className="text-xs text-[#64707d]">Dominan</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
